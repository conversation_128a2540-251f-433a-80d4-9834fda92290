import { Evaluator, EvaluationContext, EvaluationResult, EvaluationStrategy, MatchMode, PatternMatch } from '../types';
import { normalizeText, matchPattern } from '../utils/text-utils';

/**
 * Configuration for pattern evaluation
 */
export interface PatternEvaluatorConfig {
  /** Matching mode to use */
  matchMode: MatchMode;
  /** Whether to normalize text before matching */
  normalize: boolean;
  /** Weight for positive matches */
  positiveWeight: number;
  /** Weight for negative matches (overrides positive) */
  negativeWeight: number;
}

/**
 * Pattern-based evaluator following skatebench's approach
 * Checks agent output against expected answers and negative answers
 */
export class PatternEvaluator extends Evaluator {
  private config: PatternEvaluatorConfig;

  constructor(config: Partial<PatternEvaluatorConfig> = {}) {
    super(EvaluationStrategy.PATTERN);
    this.config = {
      matchMode: MatchMode.CASE_INSENSITIVE,
      normalize: true,
      positiveWeight: 1.0,
      negativeWeight: -1.0,
      ...config
    };
  }

  async evaluate(context: EvaluationContext): Promise<EvaluationResult> {
    const startTime = Date.now();
    
    try {
      const { testCase, agentOutput } = context;
      
      // Extract expected answers and negative answers from test case
      const expectedAnswers = this.extractExpectedAnswers(testCase);
      const negativeAnswers = this.extractNegativeAnswers(testCase);
      
      if (expectedAnswers.length === 0) {
        return {
          score: 0,
          success: false,
          error: 'No expected answers found in test case',
          strategy: this.strategy,
          executionTime: Date.now() - startTime
        };
      }

      // Normalize text if configured
      const normalizedOutput = this.config.normalize 
        ? normalizeText(agentOutput) 
        : agentOutput;

      // Check negative patterns first (they override positive matches)
      const negativeMatches = await this.checkPatterns(
        normalizedOutput, 
        negativeAnswers, 
        this.config.matchMode
      );

      if (negativeMatches.some(match => match.matched)) {
        return {
          score: 0,
          success: false,
          details: {
            positiveMatches: [],
            negativeMatches,
            reason: 'Negative pattern matched'
          },
          strategy: this.strategy,
          executionTime: Date.now() - startTime
        };
      }

      // Check positive patterns
      const positiveMatches = await this.checkPatterns(
        normalizedOutput,
        expectedAnswers,
        this.config.matchMode
      );

      const hasPositiveMatch = positiveMatches.some(match => match.matched);
      const score = hasPositiveMatch ? 1.0 : 0.0;

      return {
        score,
        success: hasPositiveMatch,
        details: {
          positiveMatches,
          negativeMatches,
          matchMode: this.config.matchMode,
          normalized: this.config.normalize
        },
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        score: 0,
        success: false,
        error: `Pattern evaluation failed: ${error instanceof Error ? error.message : String(error)}`,
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Extract expected answers from test case
   */
  private extractExpectedAnswers(testCase: any): string[] {
    // Support multiple formats for expected answers
    if (testCase.expectedAnswers) {
      return Array.isArray(testCase.expectedAnswers) 
        ? testCase.expectedAnswers 
        : [testCase.expectedAnswers];
    }
    
    if (testCase.answers) {
      return Array.isArray(testCase.answers) 
        ? testCase.answers 
        : [testCase.answers];
    }

    if (testCase.expected) {
      return Array.isArray(testCase.expected) 
        ? testCase.expected 
        : [testCase.expected];
    }

    return [];
  }

  /**
   * Extract negative answers from test case
   */
  private extractNegativeAnswers(testCase: any): string[] {
    if (testCase.negativeAnswers) {
      return Array.isArray(testCase.negativeAnswers) 
        ? testCase.negativeAnswers 
        : [testCase.negativeAnswers];
    }

    if (testCase.negative) {
      return Array.isArray(testCase.negative) 
        ? testCase.negative 
        : [testCase.negative];
    }

    return [];
  }

  /**
   * Check patterns against text
   */
  private async checkPatterns(
    text: string, 
    patterns: string[], 
    mode: MatchMode
  ): Promise<PatternMatch[]> {
    const results: PatternMatch[] = [];

    for (const pattern of patterns) {
      try {
        const match = matchPattern(text, pattern, mode);
        results.push({
          pattern,
          matched: match.matched,
          details: match.details
        });
      } catch (error) {
        results.push({
          pattern,
          matched: false,
          details: { error: error instanceof Error ? error.message : String(error) }
        });
      }
    }

    return results;
  }
}
