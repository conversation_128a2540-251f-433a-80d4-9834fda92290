import type { BenchmarkConfig, AgentResult } from '../config/types';
import type { CommandExecutor } from '../utils/shell';
import type { Logger } from '../utils/logger';
import type { TestSuiteReader } from '../test-suites/reader';
import { EvaluationService, EvaluationStrategy } from '../evaluation';

export class TestSuiteTestRunner {
    private evaluationService: EvaluationService;

    constructor(
        private executor: CommandExecutor,
        private testSuiteReader: TestSuiteReader,
        private logger: Logger,
        private containerName: string
    ) {
        this.evaluationService = new EvaluationService();
    }

    async run(
        config: BenchmarkConfig,
        suiteId: string,
        testCaseId: string,
        agentOutput: string,
        workspacePath: string,
        useDocker: boolean = true
    ): Promise<AgentResult> {
        const startTime = Date.now();
        const exerciseName = `${suiteId}:${testCaseId}`;
        
        try {
            this.logger.info(`Running test evaluation for ${exerciseName}`);

            // Load the test suite and find the specific test case
            const suite = await this.testSuiteReader.loadSuite(suiteId);
            const testCase = suite.tests.find(tc => tc.id === testCaseId);

            if (!testCase) {
                throw new Error(`Test case ${testCaseId} not found in suite ${suiteId}`);
            }

            // Create evaluation context
            const context = EvaluationService.createContext(
                testCase,
                agentOutput,
                workspacePath,
                { suiteId, testCaseId, exerciseName }
            );

            // Determine evaluation configuration based on test case
            const evaluationConfig = this.createEvaluationConfig(testCase, config);

            // Perform evaluation
            this.logger.info(`Evaluating agent output using strategies: ${evaluationConfig.strategies.join(', ')}`);
            const evaluationResult = await this.evaluationService.evaluateAgentOutput(context, evaluationConfig);

            const endTime = Date.now();

            this.logger.info(`Test evaluation completed for ${exerciseName}`, {
                score: evaluationResult.score,
                success: evaluationResult.success,
                strategy: evaluationResult.strategy
            });

            return {
                exercise: exerciseName,
                success: evaluationResult.success,
                output: this.formatEvaluationOutput(evaluationResult),
                error: evaluationResult.error || '',
                duration: endTime - startTime,
                score: evaluationResult.score,
                details: evaluationResult.details
            };

        } catch (error) {
            const endTime = Date.now();
            
            this.logger.error(`Test evaluation failed for ${exerciseName}: ${error}`);
            
            return {
                exercise: exerciseName,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : String(error),
                duration: endTime - startTime
            };
        }
    }

    /**
     * Create evaluation configuration based on test case and benchmark config
     */
    private createEvaluationConfig(testCase: any, config: BenchmarkConfig): any {
        // Default to pattern matching for all test cases
        const strategies = [EvaluationStrategy.PATTERN];

        // Add file evaluation if test case specifies files
        if (testCase.files || testCase.requiredFiles || testCase.expectedFiles) {
            strategies.push(EvaluationStrategy.FILE);
        }

        // Add compilation evaluation for code generation tasks
        if (this.isCodeGenerationTask(testCase)) {
            strategies.push(EvaluationStrategy.COMPILATION);
        }

        return {
            strategies,
            weights: {
                [EvaluationStrategy.PATTERN]: 0.6,
                [EvaluationStrategy.FILE]: 0.3,
                [EvaluationStrategy.COMPILATION]: 0.1,
                [EvaluationStrategy.FUNCTIONALITY]: 0.0,
                [EvaluationStrategy.COMPOSITE]: 1.0
            },
            timeout: config.timeout || 30000,
            failFast: false,
            strategyConfig: {
                [EvaluationStrategy.PATTERN]: {
                    matchMode: 'case_insensitive',
                    normalize: true
                },
                [EvaluationStrategy.FILE]: {
                    requiredExtensions: ['.ts', '.tsx', '.js', '.jsx'],
                    checkContent: true
                },
                [EvaluationStrategy.COMPILATION]: {
                    typeCheck: false,
                    timeout: 15000
                }
            }
        };
    }

    /**
     * Check if test case is a code generation task
     */
    private isCodeGenerationTask(testCase: any): boolean {
        const prompt = (testCase.prompt || '').toLowerCase();
        const codeKeywords = [
            'create', 'implement', 'write', 'build', 'generate',
            'component', 'function', 'class', 'api', 'server',
            'typescript', 'javascript', 'react', 'express'
        ];

        return codeKeywords.some(keyword => prompt.includes(keyword));
    }

    /**
     * Format evaluation result for output
     */
    private formatEvaluationOutput(result: any): string {
        const lines = [
            `Evaluation Strategy: ${result.strategy}`,
            `Score: ${(result.score * 100).toFixed(1)}%`,
            `Success: ${result.success ? 'Yes' : 'No'}`
        ];

        if (result.executionTime) {
            lines.push(`Execution Time: ${result.executionTime}ms`);
        }

        if (result.details) {
            if (result.details.positiveMatches) {
                const matches = result.details.positiveMatches.filter((m: any) => m.matched);
                lines.push(`Pattern Matches: ${matches.length}`);
            }

            if (result.details.fileResults) {
                const foundFiles = result.details.fileResults.filter((f: any) => f.exists);
                lines.push(`Files Found: ${foundFiles.length}`);
            }

            if (result.details.compilationResults) {
                const compiled = result.details.compilationResults.filter((c: any) => c.success);
                lines.push(`Files Compiled: ${compiled.length}/${result.details.compilationResults.length}`);
            }
        }

        return lines.join('\n');
    }
}
