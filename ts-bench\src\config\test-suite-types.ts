import type { AgentR<PERSON>ult } from './types';

/**
 * Extended result type for test suite evaluation with test suite specific fields
 */
export interface TestSuiteAgentResult extends AgentResult {
    /** The test suite ID */
    suiteId: string;
    /** The specific test case ID within the suite */
    testCaseId: string;
    /** The original prompt/question from the test case */
    prompt: string;
    /** The expected answer for comparison */
    expectedAnswer?: string;
    /** Evaluation score or correctness measure */
    evaluationScore?: number;
    /** Additional evaluation metadata */
    evaluationDetails?: Record<string, any>;
}

/**
 * Context information for test suite execution
 */
export interface TestSuiteExecutionContext {
    /** The test suite ID */
    suiteId: string;
    /** The test case ID */
    testCaseId: string;
    /** The test case prompt */
    prompt: string;
    /** System prompt for the suite */
    systemPrompt?: string;
    /** Expected answer */
    expectedAnswer: string;
    /** Additional test case metadata */
    metadata?: Record<string, any>;
}

/**
 * Configuration options for test suite execution
 */
export interface TestSuiteRunOptions {
    /** Whether to use Docker for execution */
    useDocker?: boolean;
    /** Timeout for agent execution in milliseconds */
    timeout?: number;
    /** Whether to include detailed evaluation */
    includeEvaluation?: boolean;
    /** Custom evaluation parameters */
    evaluationParams?: Record<string, any>;
}

/**
 * Utility functions for working with test suite results
 */
export class TestSuiteResultUtils {
    /**
     * Converts a regular AgentResult to TestSuiteAgentResult
     */
    static toTestSuiteResult(
        agentResult: AgentResult,
        context: TestSuiteExecutionContext
    ): TestSuiteAgentResult {
        return {
            ...agentResult,
            suiteId: context.suiteId,
            testCaseId: context.testCaseId,
            prompt: context.prompt,
            expectedAnswer: context.expectedAnswer
        };
    }

    /**
     * Extracts the base AgentResult from a TestSuiteAgentResult
     */
    static toAgentResult(testSuiteResult: TestSuiteAgentResult): AgentResult {
        const { suiteId, testCaseId, prompt, expectedAnswer, evaluationScore, evaluationDetails, ...agentResult } = testSuiteResult;
        return agentResult;
    }

    /**
     * Type guard to check if a result is a TestSuiteAgentResult
     */
    static isTestSuiteResult(result: AgentResult | TestSuiteAgentResult): result is TestSuiteAgentResult {
        return 'suiteId' in result && 'testCaseId' in result && 'prompt' in result;
    }

    /**
     * Extracts suite and test case IDs from exercise name
     */
    static parseExerciseName(exerciseName: string): { suiteId: string; testCaseId: string } | null {
        const parts = exerciseName.split(':');
        if (parts.length === 2) {
            return {
                suiteId: parts[0],
                testCaseId: parts[1]
            };
        }
        return null;
    }

    /**
     * Creates exercise name from suite and test case IDs
     */
    static createExerciseName(suiteId: string, testCaseId: string): string {
        return `${suiteId}:${testCaseId}`;
    }
}

/**
 * Type guard functions for distinguishing between exercise and test suite results
 */
export class TestSuiteTypeGuards {
    /**
     * Checks if an exercise name represents a test suite
     */
    static isTestSuiteExercise(exerciseName: string): boolean {
        return exerciseName.includes(':') && exerciseName.split(':').length === 2;
    }

    /**
     * Checks if a result comes from test suite execution
     */
    static isFromTestSuite(result: AgentResult): boolean {
        return this.isTestSuiteExercise(result.exercise);
    }
}
